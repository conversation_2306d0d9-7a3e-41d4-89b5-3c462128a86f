# New User Experience Flow - Test Documentation

## Overview
This document outlines the complete new user experience flow that has been implemented in Togeda.ai.

## Flow Description

### 1. User Signup
- User completes signup form with preferences
- User document is created with `newUser: true` and `firstLogin: serverTimestamp()`
- User is redirected to login page

### 2. User Login (First Time)
- User logs in with new credentials
- System checks `UserService.isNewUser(userId)` which:
  - Returns `false` if `newUser` is explicitly false
  - Returns `false` if no `firstLogin` timestamp exists
  - Returns `false` if more than 7 days have passed since `firstLogin`
  - Returns `true` otherwise
- If new user: redirects to `/welcome`
- If not new user: redirects to `/dashboard`

### 3. Welcome Page (`/welcome`)
- Shows welcome message and introduction
- Loads AI trip suggestions using `NEW_USER_ONBOARDING` category (no limits)
- Suggestions are cached in `newUserTripCache` (localStorage)
- User can:
  - Click "Create This Trip" on any suggestion → goes to squad creation
  - Click "I would like to explore the app on my own" → goes to dashboard

### 4. Squad Creation (New User Flow)
- URL: `/squads/create?newUser=true`
- Shows special message: "You don't have a squad yet, create a squad first so you can create a trip."
- After squad creation:
  - Ends new user experience (`newUser = false`)
  - Transfers `newUserTripCache` to squad-bound cache
  - Redirects to trip creation with selected suggestion

### 5. Trip Creation (New User Flow)
- URL: `/trips/create?squadId={id}&newUser=true&suggestionIndex={index}`
- Pre-populates form with suggestion data:
  - Trip name: "Trip to {destination}"
  - Destination, description, place ID, image
- After trip creation:
  - Ends new user experience (if not already ended)
  - Redirects to trip page

## Key Features

### Database Schema Changes
- Added `newUser?: boolean` to User type
- Added `firstLogin?: Timestamp` to User type
- Default values: `newUser: true`, `firstLogin: serverTimestamp()`

### AI Usage Category
- Added `NEW_USER_ONBOARDING` category with unlimited usage
- Doesn't count against user limits

### Cache System
- `newUserTripCache` in localStorage (user-specific, not squad-bound)
- Automatic transfer to squad-bound cache when squad is created
- Methods: save, get, has, clear, transfer

### Edge Cases Handled
1. **7-Day Expiration**: New user experience expires after 7 days
2. **Manual Ending**: User can skip and end experience early
3. **Existing Squads**: If user already has squads, they can select one
4. **Cache Transfer**: Suggestions transfer from new user cache to squad cache
5. **Session Management**: Selected suggestion stored in sessionStorage
6. **Error Handling**: Graceful fallbacks for all operations

## Testing Checklist

### Manual Testing Steps
1. ✅ Create new account with preferences
2. ✅ Login and verify redirect to welcome page
3. ✅ Verify AI suggestions load (3 suggestions max)
4. ✅ Click "Create This Trip" and verify squad creation flow
5. ✅ Verify trip creation pre-populates with suggestion data
6. ✅ Verify newUser flag is set to false after completion
7. ✅ Login again and verify redirect to dashboard (not welcome)
8. ✅ Test "Skip" functionality
9. ✅ Test with existing user (should go to dashboard)
10. ✅ Test 7-day expiration logic

### Technical Verification
- ✅ Build completes without errors
- ✅ TypeScript compilation successful
- ✅ No console errors in browser
- ✅ All imports and dependencies resolved
- ✅ Domain architecture maintained
- ✅ Proper error handling implemented

## Files Modified/Created

### New Files
- `lib/domains/ai-suggestions/ai-suggestions-new-user-trips.hooks.ts`
- `app/(authenticated)/welcome/page.tsx`
- `NEW_USER_FLOW_TEST.md`

### Modified Files
- `lib/domains/user/user.types.ts` - Added newUser and firstLogin fields
- `lib/domains/user/user.service.ts` - Added new user management methods
- `lib/domains/user/user.hooks.ts` - Added useNewUserExperience hook
- `lib/domains/user-ai-usage/user-ai-usage.types.ts` - Added NEW_USER_ONBOARDING category
- `lib/domains/ai-suggestions/ai-suggestions-cache.service.ts` - Added new user cache methods
- `lib/domains/ai-suggestions/ai-suggestions-cache.hooks.ts` - Added new user cache hooks
- `app/signup/actions/create-user-with-profile.ts` - Set newUser defaults
- `app/login/components/login-form.tsx` - Added new user check and redirect
- `lib/domains/auth/auth.hooks.ts` - Updated auth redirect logic
- `app/(authenticated)/squads/create/page.tsx` - Added new user flow handling
- `app/(authenticated)/trips/create/page.tsx` - Added new user flow handling

## Success Criteria
✅ All tasks completed successfully
✅ Build passes without errors
✅ New user flow is complete and functional
✅ Existing functionality remains intact
✅ Domain-driven architecture maintained
✅ Proper error handling and edge cases covered
