"use client"

import { useState, useCallback } from "react"
import { useParams } from "next/navigation"
import { useUser } from "../auth/auth.hooks"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { useRealtimeUserAIUsage } from "../user-ai-usage/user-ai-usage.realtime.hooks"
import { UserAIUsageService } from "../user-ai-usage/user-ai-usage.service"
import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import { useAISuggestionsCache } from "./ai-suggestions-cache.hooks"
import { CachedTripSuggestion } from "./ai-suggestions-cache.service"
import { TripSuggestionsHookReturn } from "./ai-suggestions.types"
import { SubscriptionErrorType } from "../user-subscription/user-subscription.types"
import { generateTripSuggestions } from "@/lib/api-client"
import { useUserPreferences } from "../user-preferences/user-preferences.hooks"
import { isValidTravelType } from "@/lib/constants/travel-types"
import { getLocationImage, getLocationImageByPlaceId, searchPlaces } from "@/lib/google-places"
import { toast } from "@/components/ui/use-toast"

/**
 * Hook for AI trip suggestions
 * @param squadId Optional squad ID (if not provided, will use the ID from the URL params)
 * @returns Trip suggestions hook return object
 */
export function useAITripSuggestions(squadId?: string): TripSuggestionsHookReturn {
  const isSubscribed = useIsUserSubscribed()
  const params = useParams()
  const id = squadId || (params.id as string)
  const currentUser = useUser()
  const { preferences } = useUserPreferences(currentUser?.uid || "")
  const { usage, getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)
  const { hasCachedSuggestions, getTripSuggestions, saveTripSuggestions } = useAISuggestionsCache()

  // State for suggestions
  const [suggestions, setSuggestions] = useState<CachedTripSuggestion[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [suggestionsLoaded, setSuggestionsLoaded] = useState(false)
  const [usingCachedSuggestions, setUsingCachedSuggestions] = useState(false)
  const [usageError, setUsageError] = useState<TripSuggestionsHookReturn["usageError"]>(null)

  /**
   * Check if the user can make an AI request
   * @param category AI usage category
   * @returns Whether the user can make a request
   */
  const canMakeRequest = useCallback(
    async (category: AIUsageCategory): Promise<boolean> => {
      if (!currentUser?.uid) return false

      // If user has a subscription, they can always make requests
      if (isSubscribed) return true

      // Check if the user has reached their limit
      const categoryUsage = getCategoryUsage(category)
      if (!categoryUsage) return true

      // If they've reached the limit, show an error
      if (categoryUsage.count >= categoryUsage.limit) {
        setUsageError({
          show: true,
          errorType: SubscriptionErrorType.TRIP_AI_LIMIT_REACHED,
          usageData: {
            daily: usage?.daily || 0,
            weekly: usage?.weekly || 0,
            dailyLimit: usage?.dailyLimit || 10,
            weeklyLimit: usage?.weeklyLimit || 50,
            categoryCount: categoryUsage.count,
            categoryLimit: categoryUsage.limit,
          },
        })
        return false
      }

      return true
    },
    [currentUser, isSubscribed, getCategoryUsage, usage, setUsageError]
  )

  /**
   * Load trip suggestions
   * @param forceRefresh Whether to force a refresh (bypass cache)
   */
  const loadSuggestions = useCallback(
    async (forceRefresh: boolean = false): Promise<void> => {
      if (!currentUser?.uid) {
        toast({
          title: "Error",
          description: "You must be logged in to generate trip suggestions.",
          variant: "destructive",
        })
        return
      }

      try {
        setLoading(true)
        setError(null)

        // If we're forcing a refresh, always make a new request
        if (forceRefresh) {
          setUsingCachedSuggestions(false)
        }
        // Otherwise, check for cached suggestions
        else if (hasCachedSuggestions(AIUsageCategory.TRIP, id, currentUser.uid)) {
          // Get cached suggestions
          const cachedSuggestions = getTripSuggestions(id, currentUser.uid)

          if (cachedSuggestions && cachedSuggestions.length > 0) {
            // Use cached suggestions
            setSuggestions(cachedSuggestions)
            setSuggestionsLoaded(true)
            setUsingCachedSuggestions(true)
            setLoading(false)
            return
          }
        }

        // Check if the user can make a request
        const canMakeAIRequest = await canMakeRequest(AIUsageCategory.TRIP)
        if (!canMakeAIRequest) {
          setLoading(false)
          return
        }

        // Use preferences from the hook
        // Prepare user preferences for the API
        const userPreferences = {
          travelPreferences: preferences?.travelPreferences || [],
          budgetRange: preferences?.budgetRange || [500, 2000],
          preferredTravelSeasons: preferences?.preferredTravelSeasons || [],
          availabilityPreferences: preferences?.availabilityPreferences || [],
          travelGroupPreferences:
            preferences?.travelGroupPreferences && preferences.travelGroupPreferences.length > 0
              ? preferences.travelGroupPreferences
              : [],
        }

        // Call the API to generate trip suggestions
        const result = await generateTripSuggestions(
          userPreferences,
          forceRefresh ? undefined : suggestions.length > 0 ? suggestions : undefined
        )

        // Process the suggestions
        const processedSuggestions = await Promise.all(
          result.map(async (suggestion) => {
            try {
              // If we have a placeId, use it to get the image
              if (suggestion.placeId) {
                try {
                  const imageResult = await getLocationImageByPlaceId(suggestion.placeId)
                  return {
                    ...suggestion,
                    image: imageResult.url,
                    attribution: imageResult.attribution,
                  }
                } catch (error) {
                  console.error(`Error fetching image for place ID ${suggestion.placeId}:`, error)
                  // Fall back to text search
                }
              }

              // Otherwise, use text search
              const imageResult = await getLocationImage(suggestion.destination)
              return {
                ...suggestion,
                image: imageResult.url,
                attribution: imageResult.attribution,
              }
            } catch (error) {
              console.error(`Error fetching image for ${suggestion.destination}:`, error)
              // Return suggestion with placeholder image
              return {
                ...suggestion,
                image: "/placeholder.svg?height=150&width=300",
              }
            }
          })
        )

        // Filter suggestions to ensure they only use valid travel types
        const validatedSuggestions = processedSuggestions.map((suggestion) => ({
          ...suggestion,
          tags: suggestion.tags.filter((tag) => isValidTravelType(tag)),
        }))

        // Cache the suggestions
        if (currentUser?.uid) {
          saveTripSuggestions(id, currentUser.uid, validatedSuggestions)
        }

        // Only increment the user's AI usage counter if we successfully generated and processed suggestions
        // and we're not using cached suggestions
        if (!usingCachedSuggestions) {
          await UserAIUsageService.incrementAIUsage(currentUser.uid, AIUsageCategory.TRIP)
        }

        setSuggestions(validatedSuggestions)
        setSuggestionsLoaded(true)
      } catch (err) {
        console.error("Error fetching trip suggestions:", err)
        setError("Failed to load trip suggestions. Please try again.")
        toast({
          title: "Error",
          description: "Failed to load trip suggestions. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    },
    [
      currentUser,
      id,
      preferences,
      hasCachedSuggestions,
      getTripSuggestions,
      saveTripSuggestions,
      canMakeRequest,
      suggestions,
      usingCachedSuggestions,
    ]
  )

  return {
    suggestions,
    loading,
    error,
    usageError,
    suggestionsLoaded,
    usingCachedSuggestions,
    loadSuggestions,
    canMakeRequest,
  }
}
