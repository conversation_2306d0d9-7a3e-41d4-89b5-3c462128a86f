"use client"

import { useState, use<PERSON><PERSON>back, useMemo } from "react"
import { useUser } from "../auth/auth.hooks"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { useRealtimeUserAIUsage } from "../user-ai-usage/user-ai-usage.realtime.hooks"
import { UserAIUsageService } from "../user-ai-usage/user-ai-usage.service"
import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import { useAISuggestionsCache } from "./ai-suggestions-cache.hooks"
import { CachedTripSuggestion } from "./ai-suggestions-cache.service"
import { TripSuggestionsHookReturn } from "./ai-suggestions.types"
import { generateTripSuggestions } from "@/lib/api-client"
import { useUserPreferences } from "../user-preferences/user-preferences.hooks"
import { isValidTravelType } from "@/lib/constants/travel-types"
import { getLocationImage, getLocationImageByPlaceId, searchPlaces } from "@/lib/google-places"
import { toast } from "@/components/ui/use-toast"

/**
 * Hook for new user AI trip suggestions (onboarding experience)
 * This hook uses the NEW_USER_ONBOARDING category which doesn't count against user limits
 * @returns Trip suggestions hook return object
 */
export function useNewUserTripSuggestions(): TripSuggestionsHookReturn {
  const isSubscribed = useIsUserSubscribed()
  const currentUser = useUser()
  const { preferences } = useUserPreferences(currentUser?.uid || "")
  const { usage, getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)

  // Memoize user preferences to prevent infinite re-renders
  const memoizedPreferences = useMemo(
    () => ({
      travelPreferences: preferences?.travelPreferences || [],
      budgetRange: Array.isArray(preferences?.budgetRange)
        ? preferences.budgetRange
        : preferences?.budgetRange === "budget-friendly"
          ? [0, 500]
          : preferences?.budgetRange === "mid-range"
            ? [500, 2000]
            : preferences?.budgetRange === "luxury"
              ? [2000, 10000]
              : [500, 2000],
      preferredTravelSeasons: preferences?.preferredTravelSeasons || [],
      availabilityPreferences: preferences?.availabilityPreferences || [],
      travelGroupPreferences:
        preferences?.travelGroupPreferences && preferences.travelGroupPreferences.length > 0
          ? preferences.travelGroupPreferences
          : [],
    }),
    [preferences]
  )
  const {
    hasNewUserTripSuggestions,
    getNewUserTripSuggestions,
    saveNewUserTripSuggestions,
    transferNewUserTripSuggestionsToSquad,
  } = useAISuggestionsCache()

  // State for suggestions
  const [suggestions, setSuggestions] = useState<CachedTripSuggestion[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [suggestionsLoaded, setSuggestionsLoaded] = useState(false)
  const [usingCachedSuggestions, setUsingCachedSuggestions] = useState(false)
  const [usageError, setUsageError] = useState<TripSuggestionsHookReturn["usageError"]>(null)

  /**
   * Check if the user can make an AI request (always true for new user onboarding)
   * @param category AI usage category
   * @returns Whether the user can make a request
   */
  const canMakeRequest = useCallback(
    async (category: AIUsageCategory): Promise<boolean> => {
      if (!currentUser?.uid) return false

      // For new user onboarding, always allow requests (no limits)
      if (category === AIUsageCategory.NEW_USER_ONBOARDING) {
        return true
      }

      // For other categories, use normal logic
      try {
        const canMake = await UserAIUsageService.canMakeAIRequest(
          currentUser.uid,
          isSubscribed,
          category
        )
        return canMake
      } catch (error) {
        console.error("Error checking AI request permission:", error)
        return false
      }
    },
    [currentUser?.uid, isSubscribed]
  )

  /**
   * Load trip suggestions for new users
   * @param forceRefresh Whether to force refresh suggestions
   */
  const loadSuggestions = useCallback(
    async (forceRefresh: boolean = false) => {
      if (!currentUser?.uid) {
        setError("You must be logged in to get trip suggestions")
        return
      }

      try {
        setLoading(true)
        setError(null)

        // If we're forcing a refresh, always make a new request
        if (forceRefresh) {
          setUsingCachedSuggestions(false)
        }
        // Otherwise, check for cached suggestions
        else if (hasNewUserTripSuggestions(currentUser.uid)) {
          // Get cached suggestions
          const cachedSuggestions = getNewUserTripSuggestions(currentUser.uid)

          if (cachedSuggestions && cachedSuggestions.length > 0) {
            // Use cached suggestions
            setSuggestions(cachedSuggestions)
            setSuggestionsLoaded(true)
            setUsingCachedSuggestions(true)
            setLoading(false)
            return
          }
        }

        // Check if the user can make a request (should always be true for new user onboarding)
        const canMakeAIRequest = await canMakeRequest(AIUsageCategory.NEW_USER_ONBOARDING)
        if (!canMakeAIRequest) {
          setError("Unable to generate suggestions at this time")
          setLoading(false)
          return
        }

        // Call the API to generate trip suggestions
        const apiSuggestions = await generateTripSuggestions(
          memoizedPreferences,
          forceRefresh ? undefined : suggestions.length > 0 ? suggestions : undefined
        )

        if (!apiSuggestions || !Array.isArray(apiSuggestions)) {
          throw new Error("Failed to generate trip suggestions")
        }

        // Process and validate suggestions
        const validatedSuggestions: CachedTripSuggestion[] = []

        for (const suggestion of apiSuggestions) {
          try {
            // Validate travel type
            if (
              suggestion.tags &&
              suggestion.tags.length > 0 &&
              !suggestion.tags.every((tag) => isValidTravelType(tag))
            ) {
              console.warn("Invalid travel type in suggestion:", suggestion.tags)
              continue
            }

            let imageUrl = suggestion.imageUrl || ""
            let attribution = undefined

            // If no image provided or it's a placeholder, try to get a real image
            if (!imageUrl || imageUrl.includes("placeholder") || imageUrl.includes("example")) {
              try {
                // First try to get image by place ID if available
                if (suggestion.placeId) {
                  const imageResult = await getLocationImageByPlaceId(suggestion.placeId)
                  if (imageResult.success && imageResult.imageUrl) {
                    imageUrl = imageResult.imageUrl
                    attribution = imageResult.attribution
                  }
                } else {
                  // Fallback to search by destination name
                  const imageResult = await getLocationImage(suggestion.destination)
                  if (imageResult.success && imageResult.imageUrl) {
                    imageUrl = imageResult.imageUrl
                    attribution = imageResult.attribution
                  }
                }
              } catch (imageError) {
                console.warn(
                  "Failed to get image for destination:",
                  suggestion.destination,
                  imageError
                )
                // Continue without image
              }
            }

            validatedSuggestions.push({
              ...suggestion,
              image: imageUrl,
              attribution,
            })
          } catch (suggestionError) {
            console.error("Error processing suggestion:", suggestionError)
            // Skip this suggestion and continue
          }
        }

        if (validatedSuggestions.length === 0) {
          throw new Error("No valid suggestions were generated")
        }

        // Save to new user cache
        saveNewUserTripSuggestions(currentUser.uid, validatedSuggestions)

        // Only increment the user's AI usage counter if we successfully generated and processed suggestions
        // and we're not using cached suggestions
        if (!usingCachedSuggestions) {
          await UserAIUsageService.incrementAIUsage(
            currentUser.uid,
            AIUsageCategory.NEW_USER_ONBOARDING
          )
        }

        setSuggestions(validatedSuggestions)
        setSuggestionsLoaded(true)
      } catch (err) {
        console.error("Error fetching new user trip suggestions:", err)
        setError("Failed to load trip suggestions. Please try again.")
        toast({
          title: "Error",
          description: "Failed to load trip suggestions. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    },
    [
      currentUser?.uid,
      memoizedPreferences,
      hasNewUserTripSuggestions,
      getNewUserTripSuggestions,
      saveNewUserTripSuggestions,
      canMakeRequest,
    ]
  )

  /**
   * Transfer new user suggestions to a squad-bound cache
   * @param squadId Squad ID to transfer suggestions to
   * @returns Transferred suggestions or null
   */
  const transferToSquad = useCallback(
    (squadId: string): CachedTripSuggestion[] | null => {
      if (!currentUser?.uid) return null
      return transferNewUserTripSuggestionsToSquad(currentUser.uid, squadId)
    },
    [currentUser?.uid, transferNewUserTripSuggestionsToSquad]
  )

  // Calculate if we should show AI suggestions
  const showAiSuggestions = !loading && !error && suggestionsLoaded && suggestions.length > 0

  return {
    suggestions,
    loading,
    error,
    usageError,
    showAiSuggestions: showAiSuggestions,
    usingCachedSuggestions,
    loadSuggestions,
    transferToSquad,
  }
}
