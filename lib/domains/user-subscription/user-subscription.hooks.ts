"use client"

import { useEffect } from "react"
import { useUserSubscriptionStore } from "./user-subscription.store"
import { useUser } from "../auth/auth.hooks"
import { useRouter } from "next/navigation"
import { SubscriptionErrorType } from "./user-subscription.types"

// Export real-time hooks
export * from "./user-subscription.realtime.hooks"

/**
 * Selectors for user subscription store
 */

// Basic state selectors
export const useUserSubscription = () => useUserSubscriptionStore()
export const useUserSubscriptionLoading = () => useUserSubscriptionStore((state) => state.loading)
export const useIsUserSubscribed = () => useUserSubscriptionStore((state) => state.isSubscribed)
export const useUserSubscriptionPlan = () =>
  useUserSubscriptionStore((state) => state.subscriptionPlan)
export const useUserSubscriptionStatus = () =>
  useUserSubscriptionStore((state) => state.subscriptionStatus)
export const useUserSubscriptionDetails = () =>
  useUserSubscriptionStore((state) => state.subscription)

// Limit selectors
export const useMaxSquads = () => useUserSubscriptionStore((state) => state.maxSquads)
export const useMaxTripsPerSquad = () => useUserSubscriptionStore((state) => state.maxTripsPerSquad)
export const useMaxDailyAIRequests = () =>
  useUserSubscriptionStore((state) => state.maxDailyAIRequests)
export const useMaxWeeklyAIRequests = () =>
  useUserSubscriptionStore((state) => state.maxWeeklyAIRequests)
export const useHasTripChat = () => useUserSubscriptionStore((state) => state.hasTripChat)

// Action selectors
export const useFetchUserSubscription = () =>
  useUserSubscriptionStore((state) => state.fetchSubscription)
export const useCanCreateMoreSquads = () =>
  useUserSubscriptionStore((state) => state.canCreateMoreSquads)
export const useCanCreateMoreTripsInSquad = () =>
  useUserSubscriptionStore((state) => state.canCreateMoreTripsInSquad)

/**
 * Enhanced user subscription hook with auto-initialization
 */
export const useUserSubscriptionWithInit = () => {
  const user = useUser()
  const router = useRouter()
  const store = useUserSubscriptionStore()
  const { fetchSubscription, refreshSubscriptionIfNeeded } = store

  // Fetch subscription data when auth state changes
  useEffect(() => {
    if (user?.uid) {
      fetchSubscription(user.uid)
    }
  }, [user, fetchSubscription])

  // Add a periodic refresh for very long sessions
  useEffect(() => {
    if (!user?.uid) return

    // Check when component mounts and when window regains focus
    refreshSubscriptionIfNeeded(user.uid)

    const handleFocus = () => {
      refreshSubscriptionIfNeeded(user.uid)
    }

    window.addEventListener("focus", handleFocus)
    return () => window.removeEventListener("focus", handleFocus)
  }, [user, refreshSubscriptionIfNeeded])

  // Create a router-aware error handler
  const handleSubscriptionError = (errorType: SubscriptionErrorType) => {
    store.handleSubscriptionErrorWithRouter(errorType, router)
  }

  // Return the store with the router-aware error handler
  return {
    ...store,
    handleSubscriptionError,
  }
}
