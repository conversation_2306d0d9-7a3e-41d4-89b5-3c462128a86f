import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
  Timestamp,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { Trip, TripCreateData, TripUpdateData } from "./trip.types"
import { UserTripService } from "../user-trip/user-trip.service"
import { normalizeToUTCMidnight } from "@/lib/utils/date-utils"

/**
 * Trip service for Firebase operations
 */
export class TripService {
  private static readonly COLLECTION = "trips"

  /**
   * Create a new trip
   * @param tripData Trip data
   * @returns The new trip ID
   */
  static async createTrip(tripData: TripCreateData): Promise<string> {
    try {
      const tripRef = doc(collection(db, this.COLLECTION))
      const tripId = tripRef.id

      // Convert dates to Date objects if they're Timestamps, otherwise use as-is
      const startDate =
        tripData.startDate instanceof Date
          ? tripData.startDate
          : (tripData.startDate as any).toDate()
      const endDate =
        tripData.endDate instanceof Date ? tripData.endDate : (tripData.endDate as any).toDate()

      // Normalize dates to UTC midnight to ensure consistent storage regardless of user timezone
      const normalizedData = {
        ...tripData,
        startDate: Timestamp.fromDate(normalizeToUTCMidnight(startDate)),
        endDate: Timestamp.fromDate(normalizeToUTCMidnight(endDate)),
      }

      await setDoc(tripRef, {
        ...normalizedData,
        id: tripId,
        tasksCompleted: 0,
        totalTasks: 0,
        createdAt: serverTimestamp(),
      })

      return tripId
    } catch (error) {
      console.error("Error creating trip:", error)
      throw error
    }
  }

  /**
   * Get a trip by ID
   * @param tripId Trip ID
   * @returns The trip data or null if not found
   */
  static async getTrip(tripId: string): Promise<Trip | null> {
    try {
      const tripDoc = await getDoc(doc(db, this.COLLECTION, tripId))

      if (tripDoc.exists()) {
        const data = tripDoc.data()

        // Get the actual attendees from userTrips collection
        const attendees = await UserTripService.getTripAttendees(tripId)

        return { ...data, id: tripId, attendees } as Trip
      }

      return null
    } catch (error) {
      console.error("Error getting trip:", error)
      throw error
    }
  }

  /**
   * Get trips for a user
   * @param userId User ID
   * @returns Array of trips
   */
  static async getUserTrips(userId: string): Promise<Trip[]> {
    try {
      // Query trips where the user is in the attendees array
      const q = query(collection(db, this.COLLECTION), where("attendees", "array-contains", userId))
      const querySnapshot = await getDocs(q)

      // Process each trip and ensure the attendees field is up-to-date
      const trips = await Promise.all(
        querySnapshot.docs.map(async (doc) => {
          const data = doc.data()
          const tripId = doc.id

          // Get the actual attendees from userTrips collection
          const attendees = await UserTripService.getTripAttendees(tripId)

          // Ensure tasksCompleted and totalTasks have default values
          const tasksCompleted = typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0
          const totalTasks = typeof data.totalTasks === "number" ? data.totalTasks : 0

          return {
            ...data,
            id: tripId,
            attendees,
            tasksCompleted,
            totalTasks,
          } as Trip
        })
      )

      return trips
    } catch (error) {
      console.error("Error getting user trips:", error)
      throw error
    }
  }

  /**
   * Get trips for a squad
   * @param squadId Squad ID
   * @returns Array of trips
   */
  static async getSquadTrips(squadId: string): Promise<Trip[]> {
    try {
      const q = query(collection(db, this.COLLECTION), where("squadId", "==", squadId))
      const querySnapshot = await getDocs(q)

      // Process each trip and ensure the attendees field is up-to-date
      const trips = await Promise.all(
        querySnapshot.docs.map(async (doc) => {
          const data = doc.data()
          const tripId = doc.id

          // Get the actual attendees from userTrips collection
          const attendees = await UserTripService.getTripAttendees(tripId)

          // Ensure tasksCompleted and totalTasks have default values
          const tasksCompleted = typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0
          const totalTasks = typeof data.totalTasks === "number" ? data.totalTasks : 0

          return {
            ...data,
            id: tripId,
            attendees,
            tasksCompleted,
            totalTasks,
          } as Trip
        })
      )

      return trips
    } catch (error) {
      console.error("Error getting squad trips:", error)
      throw error
    }
  }

  /**
   * Update a trip
   * @param tripId Trip ID
   * @param tripData Trip data to update
   * @returns Service response
   */
  static async updateTrip(tripId: string, tripData: TripUpdateData): Promise<ServiceResponse> {
    try {
      // Normalize dates to UTC midnight if they are being updated
      const normalizedData = { ...tripData }

      if (normalizedData.startDate) {
        const startDate =
          normalizedData.startDate instanceof Date
            ? normalizedData.startDate
            : (normalizedData.startDate as any).toDate()
        normalizedData.startDate = Timestamp.fromDate(normalizeToUTCMidnight(startDate))
      }

      if (normalizedData.endDate) {
        const endDate =
          normalizedData.endDate instanceof Date
            ? normalizedData.endDate
            : (normalizedData.endDate as any).toDate()
        normalizedData.endDate = Timestamp.fromDate(normalizeToUTCMidnight(endDate))
      }

      await updateDoc(doc(db, this.COLLECTION, tripId), {
        ...normalizedData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating trip:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user is the trip leader
   * @param userId User ID
   * @param tripId Trip ID
   * @returns True if the user is the trip leader
   */
  static async isUserTripLeader(userId: string, tripId: string): Promise<boolean> {
    try {
      const trip = await this.getTrip(tripId)
      return trip?.leaderId === userId
    } catch (error) {
      console.error("Error checking if user is trip leader:", error)
      return false
    }
  }

  /**
   * Check if a user can delete a trip (must be trip leader)
   * @param userId User ID
   * @param tripId Trip ID
   * @returns True if the user can delete the trip
   */
  static async canUserDeleteTrip(userId: string, tripId: string): Promise<boolean> {
    return this.isUserTripLeader(userId, tripId)
  }

  /**
   * Delete a trip and all related data
   * @param tripId Trip ID
   * @param userId User ID (for authorization)
   * @returns Service response
   */
  static async deleteTrip(tripId: string, userId: string): Promise<ServiceResponse> {
    try {
      // Check if user can delete the trip
      const canDelete = await this.canUserDeleteTrip(userId, tripId)
      if (!canDelete) {
        return {
          success: false,
          error: new Error("Unauthorized: Only trip leader can delete the trip"),
        }
      }

      // Check if trip exists
      const trip = await this.getTrip(tripId)
      if (!trip) {
        return { success: false, error: new Error("Trip not found") }
      }

      // Delete related data in parallel for better performance
      await Promise.all([
        // Delete all tasks for this trip
        this.deleteTripTasks(tripId),
        // Delete all itinerary items for this trip
        this.deleteTripItineraries(tripId),
        // Delete all user trip statuses for this trip
        this.deleteTripUserStatuses(tripId),
        // Delete all trip savings and related transactions for this trip
        this.deleteTripSavingsAndTransactions(tripId),
      ])

      // Finally, delete the trip itself
      await deleteDoc(doc(db, this.COLLECTION, tripId))

      return { success: true }
    } catch (error) {
      console.error("Error deleting trip:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete all tasks for a trip
   * @param tripId Trip ID
   */
  private static async deleteTripTasks(tripId: string): Promise<void> {
    const q = query(collection(db, "tripTasks"), where("tripId", "==", tripId))
    const querySnapshot = await getDocs(q)

    const deletePromises = querySnapshot.docs.map((doc) => deleteDoc(doc.ref))
    await Promise.all(deletePromises)
  }

  /**
   * Delete all itinerary items for a trip
   * @param tripId Trip ID
   */
  private static async deleteTripItineraries(tripId: string): Promise<void> {
    const q = query(collection(db, "tripItineraries"), where("tripId", "==", tripId))
    const querySnapshot = await getDocs(q)

    const deletePromises = querySnapshot.docs.map((doc) => deleteDoc(doc.ref))
    await Promise.all(deletePromises)
  }

  /**
   * Delete all user trip statuses for a trip
   * @param tripId Trip ID
   */
  private static async deleteTripUserStatuses(tripId: string): Promise<void> {
    const q = query(collection(db, "userTrips"), where("tripId", "==", tripId))
    const querySnapshot = await getDocs(q)

    const deletePromises = querySnapshot.docs.map((doc) => deleteDoc(doc.ref))
    await Promise.all(deletePromises)
  }

  /**
   * Delete all trip savings and related transactions for a trip
   * @param tripId Trip ID
   */
  private static async deleteTripSavingsAndTransactions(tripId: string): Promise<void> {
    // First, get all trip savings for this trip
    const savingsQuery = query(collection(db, "tripSavings"), where("tripId", "==", tripId))
    const savingsSnapshot = await getDocs(savingsQuery)

    // Get all savings IDs to delete their transactions
    const savingsIds = savingsSnapshot.docs.map((doc) => doc.id)

    // Delete all transactions for these savings in parallel
    const transactionDeletePromises = savingsIds.map(async (savingsId) => {
      const transactionsQuery = query(
        collection(db, "savingsTransactions"),
        where("savingsId", "==", savingsId)
      )
      const transactionsSnapshot = await getDocs(transactionsQuery)
      const deletePromises = transactionsSnapshot.docs.map((doc) => deleteDoc(doc.ref))
      return Promise.all(deletePromises)
    })

    // Delete all savings
    const savingsDeletePromises = savingsSnapshot.docs.map((doc) => deleteDoc(doc.ref))

    // Wait for all deletions to complete
    await Promise.all([...transactionDeletePromises, ...savingsDeletePromises])
  }
}
