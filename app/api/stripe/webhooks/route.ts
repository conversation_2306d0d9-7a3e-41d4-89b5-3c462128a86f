import { NextRequest, NextResponse } from "next/server"
import { stripe } from "@/lib/server/stripe"
import { getAdminInstance } from "@/lib/firebase-admin"
import Strip<PERSON> from "stripe"

// Function to find a user by Stripe customer ID
async function getUserByStripeCustomerId(customerId: string) {
  try {
    const { adminDb } = await getAdminInstance()
    if (!adminDb) {
      console.error("Firebase Admin Firestore is not initialized")
      return null
    }

    // First try to find in userSubscriptions collection
    const subscriptionsRef = adminDb.collection("userSubscriptions")
    let snapshot = await subscriptionsRef.where("stripeCustomerId", "==", customerId).limit(1).get()

    if (!snapshot.empty) {
      const subscriptionDoc = snapshot.docs[0]
      const userId = subscriptionDoc.id // Same as userId in userSubscriptions
      console.log(`Found user ${userId} in userSubscriptions with customer ID: ${customerId}`)
      return { uid: userId }
    }

    // Fallback to users collection for backward compatibility
    console.log(`No subscription found with customer ID: ${customerId}, checking users collection`)
    const usersRef = adminDb.collection("users")
    snapshot = await usersRef.where("stripeCustomerId", "==", customerId).limit(1).get()

    if (snapshot.empty) {
      console.log(`No user found with Stripe customer ID: ${customerId}`)
      return null
    }

    const userDoc = snapshot.docs[0]
    return { ...userDoc.data(), uid: userDoc.id }
  } catch (error) {
    console.error("Error finding user by Stripe customer ID:", error)
    return null
  }
}

// Function to update user subscription data
async function updateUserSubscription(userId: string, subscriptionData: any) {
  try {
    const { adminDb, adminFieldValue } = await getAdminInstance()
    if (!adminDb) {
      console.error("Firebase Admin Firestore is not initialized")
      return { success: false }
    }

    // Log the data we're about to save
    console.log(
      `Updating subscription for user ${userId} with data:`,
      JSON.stringify(subscriptionData)
    )

    // Convert Date objects to timestamps that Firestore can handle
    const dataToUpdate = { ...subscriptionData }

    // Handle subscriptionCurrentPeriodEnd conversion
    if (dataToUpdate.subscriptionCurrentPeriodEnd) {
      console.log(
        `Processing subscriptionCurrentPeriodEnd:`,
        typeof dataToUpdate.subscriptionCurrentPeriodEnd,
        dataToUpdate.subscriptionCurrentPeriodEnd
      )

      if (dataToUpdate.subscriptionCurrentPeriodEnd instanceof Date) {
        // Store as a numeric seconds timestamp for consistency
        const seconds = Math.floor(dataToUpdate.subscriptionCurrentPeriodEnd.getTime() / 1000)
        if (isNaN(seconds)) {
          console.error("Invalid date conversion resulted in NaN, setting to null")
          dataToUpdate.subscriptionCurrentPeriodEnd = null
        } else {
          dataToUpdate.subscriptionCurrentPeriodEnd = seconds
          console.log(`Converted date to seconds timestamp: ${seconds}`)
        }
      } else if (typeof dataToUpdate.subscriptionCurrentPeriodEnd === "number") {
        // Already a number, make sure it's valid
        if (isNaN(dataToUpdate.subscriptionCurrentPeriodEnd)) {
          console.error("subscriptionCurrentPeriodEnd is NaN, setting to null")
          dataToUpdate.subscriptionCurrentPeriodEnd = null
        }
      } else {
        // Not a Date or number, set to null
        console.error(
          `Unexpected type for subscriptionCurrentPeriodEnd: ${typeof dataToUpdate.subscriptionCurrentPeriodEnd}, setting to null`
        )
        dataToUpdate.subscriptionCurrentPeriodEnd = null
      }
    }

    // Check if user has a subscription document
    const subscriptionRef = adminDb.collection("userSubscriptions").doc(userId)
    const subscriptionDoc = await subscriptionRef.get()

    if (subscriptionDoc.exists) {
      // Update existing subscription document
      await subscriptionRef.update({
        ...dataToUpdate,
        updatedAt: adminFieldValue.serverTimestamp(),
      })
      console.log(`Updated existing subscription document for user ${userId}`)
    } else {
      // Create new subscription document
      await subscriptionRef.set({
        userId,
        stripeCustomerId: dataToUpdate.stripeCustomerId || "",
        subscriptionId: dataToUpdate.subscriptionId || "",
        subscriptionStatus: dataToUpdate.subscriptionStatus || null,
        subscriptionPlan: dataToUpdate.subscriptionPlan || "free",
        subscriptionCurrentPeriodEnd: dataToUpdate.subscriptionCurrentPeriodEnd || null,
        createdAt: adminFieldValue.serverTimestamp(),
        updatedAt: adminFieldValue.serverTimestamp(),
      })
      console.log(`Created new subscription document for user ${userId}`)
    }

    // For backward compatibility, also update the user document
    try {
      const userRef = adminDb.collection("users").doc(userId)
      await userRef.update(dataToUpdate)
      console.log(`Updated user document for backward compatibility: ${userId}`)
    } catch (error) {
      console.warn(`Could not update user document for backward compatibility: ${error}`)
      // Don't fail if this update fails
    }

    return { success: true }
  } catch (error) {
    console.error(`Error updating subscription for user ${userId}:`, error)
    return { success: false }
  }
}

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = request.headers.get("stripe-signature") as string

  if (!signature) {
    return NextResponse.json({ error: "Missing stripe-signature header" }, { status: 400 })
  }

  // Initialize Firebase Admin
  const { adminDb, adminFieldValue } = await getAdminInstance()
  if (!adminDb) {
    console.error("Firebase Admin Firestore is not initialized")
    return NextResponse.json({ error: "Server configuration error" }, { status: 500 })
  }

  try {
    // Verify the webhook signature
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )

    // Handle the event
    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as any
        let userId = session.metadata?.userId
        const customerId = session.customer
        const subscriptionId = session.subscription

        console.log(`Checkout session completed: ${session.id}`)
        console.log(`Customer ID: ${customerId}, Subscription ID: ${subscriptionId}`)

        // Update the user's subscription information
        if (customerId && subscriptionId) {
          // If userId is not in metadata, try to find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)
            } else {
              console.error(`Could not find user for customer ID ${customerId}`)
              break
            }
          }

          // Get the subscription details
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }
          const plan = subscription.items.data[0].plan
          const planInterval = plan.interval
          // Get current_period_end from Stripe and convert to Date
          console.log(
            `Stripe current_period_end:`,
            subscription.current_period_end,
            typeof subscription.current_period_end
          )

          let currentPeriodEnd = null
          if (subscription.current_period_end) {
            // Ensure it's a number before multiplying
            const periodEndTimestamp = Number(subscription.current_period_end)
            if (!isNaN(periodEndTimestamp)) {
              currentPeriodEnd = new Date(periodEndTimestamp * 1000)
              console.log(`Converted to Date:`, currentPeriodEnd)
            } else {
              console.error(
                `Invalid current_period_end from Stripe:`,
                subscription.current_period_end
              )
            }
          }

          // Add userId to subscription metadata if it's not there
          if (!subscription.metadata?.userId) {
            console.log(`Adding userId ${userId} to subscription metadata`)
            await stripe.subscriptions.update(subscriptionId, {
              metadata: { userId },
            })
          }

          // Convert the date to a timestamp that Firebase Admin can handle
          const result = await updateUserSubscription(userId, {
            stripeCustomerId: customerId,
            subscriptionId: subscriptionId,
            subscriptionStatus: "active",
            subscriptionPlan: planInterval === "year" ? "yearly" : "monthly",
            subscriptionCurrentPeriodEnd: currentPeriodEnd,
          })

          if (!result.success) {
            console.error(`Failed to update subscription for user ${userId}`)
          } else {
            console.log(`Successfully updated subscription for user ${userId}`)
          }
        } else {
          console.error("Missing customer ID or subscription ID in checkout session")
        }
        break
      }

      case "customer.subscription.updated": {
        const subscription = event.data.object as any & { current_period_end?: number }
        const customerId = subscription.customer
        const subscriptionId = subscription.id
        const status = subscription.status
        const planInterval = subscription.items.data[0].plan.interval
        console.log(`Subscription updated: ${subscriptionId}, Status: ${status}`)

        // Get current_period_end from Stripe and convert to Date
        console.log(
          `Stripe current_period_end:`,
          subscription.current_period_end,
          typeof subscription.current_period_end
        )

        let currentPeriodEnd = null
        if (subscription.current_period_end) {
          // Ensure it's a number before multiplying
          const periodEndTimestamp = Number(subscription.current_period_end)
          if (!isNaN(periodEndTimestamp)) {
            currentPeriodEnd = new Date(periodEndTimestamp * 1000)
            console.log(`Converted to Date:`, currentPeriodEnd)
          } else {
            console.error(
              `Invalid current_period_end from Stripe:`,
              subscription.current_period_end
            )
          }
        }

        // Try to get userId from metadata first
        let userId = subscription.metadata?.userId

        // If not in metadata, find the user by customer ID
        if (!userId) {
          console.log("No userId in metadata, searching by customer ID")
          const user = await getUserByStripeCustomerId(customerId)
          if (user) {
            userId = user.uid
            console.log(`Found user ${userId} by customer ID ${customerId}`)

            // Add userId to subscription metadata for future events
            await stripe.subscriptions.update(subscriptionId, {
              metadata: { userId },
            })
          }
        }

        if (userId) {
          const result = await updateUserSubscription(userId, {
            subscriptionStatus: status,
            subscriptionPlan: planInterval === "year" ? "yearly" : "monthly",
            subscriptionCurrentPeriodEnd: currentPeriodEnd,
          })

          if (!result.success) {
            console.error(`Failed to update subscription for user ${userId}`)
          } else {
            console.log(`Successfully updated subscription status to ${status} for user ${userId}`)
          }
        } else {
          console.error(`Could not find user for customer ID ${customerId}`)
        }
        break
      }

      case "customer.subscription.deleted": {
        const subscription = event.data.object as any
        const customerId = subscription.customer
        const subscriptionId = subscription.id

        console.log(`Subscription deleted: ${subscriptionId}`)

        // Try to get userId from metadata first
        let userId = subscription.metadata?.userId

        // If not in metadata, find the user by customer ID
        if (!userId) {
          console.log("No userId in metadata, searching by customer ID")
          const user = await getUserByStripeCustomerId(customerId)
          if (user) {
            userId = user.uid
            console.log(`Found user ${userId} by customer ID ${customerId}`)
          }
        }

        if (userId) {
          const result = await updateUserSubscription(userId, {
            subscriptionStatus: "canceled",
            subscriptionPlan: "free",
            subscriptionCurrentPeriodEnd: null,
          })

          if (!result.success) {
            console.error(`Failed to cancel subscription for user ${userId}`)
          } else {
            console.log(`Successfully canceled subscription for user ${userId}`)
          }
        } else {
          console.error(`Could not find user for customer ID ${customerId}`)
        }
        break
      }

      case "invoice.payment_succeeded": {
        const invoice = event.data.object as any
        const customerId = invoice.customer
        const subscriptionId = invoice.subscription

        console.log(`Invoice payment succeeded: ${invoice.id}`)

        if (subscriptionId) {
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Try to get userId from metadata first
          let userId = subscription.metadata?.userId

          // If not in metadata, find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)

              // Add userId to subscription metadata for future events
              await stripe.subscriptions.update(subscriptionId, {
                metadata: { userId },
              })
            }
          }

          if (userId) {
            // Get current_period_end from Stripe and convert to Date
            console.log(
              `Stripe current_period_end:`,
              subscription.current_period_end,
              typeof subscription.current_period_end
            )

            let currentPeriodEnd = null
            if (subscription.current_period_end) {
              // Ensure it's a number before multiplying
              const periodEndTimestamp = Number(subscription.current_period_end)
              if (!isNaN(periodEndTimestamp)) {
                currentPeriodEnd = new Date(periodEndTimestamp * 1000)
                console.log(`Converted to Date:`, currentPeriodEnd)
              } else {
                console.error(
                  `Invalid current_period_end from Stripe:`,
                  subscription.current_period_end
                )
              }
            }
            const result = await updateUserSubscription(userId, {
              subscriptionStatus: "active",
              subscriptionCurrentPeriodEnd: currentPeriodEnd,
            })

            if (!result.success) {
              console.error(`Failed to update subscription after payment for user ${userId}`)
            } else {
              console.log(`Successfully updated subscription after payment for user ${userId}`)
            }
          } else {
            console.error(`Could not find user for customer ID ${customerId}`)
          }
        } else {
          console.log("No subscription ID in invoice, might be a one-time payment")
        }
        break
      }

      case "invoice.payment_failed": {
        const invoice = event.data.object as any
        const customerId = invoice.customer
        const subscriptionId = invoice.subscription

        console.log(`Invoice payment failed: ${invoice.id}`)

        if (subscriptionId) {
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Try to get userId from metadata first
          let userId = subscription.metadata?.userId

          // If not in metadata, find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)

              // Add userId to subscription metadata for future events
              await stripe.subscriptions.update(subscriptionId, {
                metadata: { userId },
              })
            }
          }

          if (userId) {
            const result = await updateUserSubscription(userId, {
              subscriptionStatus: "past_due",
            })

            if (!result.success) {
              console.error(`Failed to update subscription to past_due for user ${userId}`)
            } else {
              console.log(`Successfully updated subscription to past_due for user ${userId}`)
            }
          } else {
            console.error(`Could not find user for customer ID ${customerId}`)
          }
        } else {
          console.log("No subscription ID in invoice")
        }
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true, type: event.type })
  } catch (error) {
    console.error("Error handling webhook:", error)

    // Check if this is a webhook signature verification error
    if (error instanceof Stripe.errors.StripeSignatureVerificationError) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    }

    const errorMessage = error instanceof Error ? error.message : "Unknown error"

    return NextResponse.json(
      { error: "Failed to handle webhook", message: errorMessage },
      { status: 500 }
    )
  }
}
