"use client"

import { useEffect, useState, useCallback } from "react"
import { useRout<PERSON>, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>rkles, ArrowRight, Eye, SkipForward, RefreshCw } from "lucide-react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useNewUserExperience } from "@/lib/domains/user/user.hooks"
import { useNewUserTripSuggestions } from "@/lib/domains/ai-suggestions/ai-suggestions-new-user-trips.hooks"
import { OptimizedImage } from "@/components/optimized-image"
import { PageLoading } from "@/components/page-loading"
import { toast } from "@/components/ui/use-toast"

export default function WelcomePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const user = useUser()
  const { isNewUser, checking, endNewUserExperience } = useNewUserExperience()
  const {
    suggestions,
    loading: suggestionsLoading,
    error: suggestionsError,
    showAiSuggestions,
    loadSuggestions,
  } = useNewUserTripSuggestions()

  const [selectedSuggestion, setSelectedSuggestion] = useState<number | null>(null)
  const [redirecting, setRedirecting] = useState(false)
  const [isNavigatingAway, setIsNavigatingAway] = useState(false)

  // Check if user should be on this page
  useEffect(() => {
    if (!checking && isNewUser === false && !isNavigatingAway) {
      // User is not new, redirect to dashboard
      router.push("/dashboard")
    }
  }, [isNewUser, checking, router, isNavigatingAway])

  // Load suggestions when component mounts
  useEffect(() => {
    if (user?.uid && isNewUser === true && !suggestionsLoading && !showAiSuggestions) {
      loadSuggestions()
    }
  }, [user?.uid, isNewUser, suggestionsLoading, showAiSuggestions, loadSuggestions])

  const handleCreateTrip = useCallback(
    async (suggestionIndex: number) => {
      if (!user) return

      setRedirecting(true)
      setIsNavigatingAway(true)

      try {
        // Store selected suggestion index in sessionStorage for trip creation
        sessionStorage.setItem("selectedNewUserSuggestion", suggestionIndex.toString())

        // End new user experience
        await endNewUserExperience()

        // Redirect to squad creation with new user flag
        router.push("/squads/create?newUser=true")
      } catch (error) {
        console.error("Error handling create trip:", error)
        toast({
          title: "Error",
          description: "Something went wrong. Please try again.",
          variant: "destructive",
        })
        setRedirecting(false)
        setIsNavigatingAway(false)
      }
    },
    [user, endNewUserExperience, router]
  )

  const handleSkip = useCallback(async () => {
    if (!user) return

    setRedirecting(true)
    setIsNavigatingAway(true)

    try {
      // End new user experience
      await endNewUserExperience()

      toast({
        title: "Welcome to Togeda.ai!",
        description: "You can explore the app at your own pace.",
      })

      // Redirect to dashboard
      router.push("/dashboard")
    } catch (error) {
      console.error("Error handling skip:", error)
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
      setRedirecting(false)
      setIsNavigatingAway(false)
    }
  }, [user, endNewUserExperience, router])

  // Show loading while checking user status or redirecting
  if (checking || isNewUser === false || redirecting) {
    return <PageLoading message="Loading..." />
  }

  // Show error if user is not authenticated
  if (!user) {
    return <PageLoading message="Authenticating..." />
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="h-8 w-8 text-primary mr-2" />
            <h1 className="text-4xl font-bold text-foreground">Welcome to Togeda.ai!</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            As an introduction to the app, we'll let you experience first hand what Togeda.ai is. As
            your first trip, here's what we suggest for your next adventure based on your
            preferences:
          </p>
        </div>

        {/* Trip Suggestions */}
        <div className="mb-8">
          {suggestionsLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <div className="aspect-video relative">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="pt-4">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="flex gap-2 mb-4">
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-2/3 mb-4" />
                    <Skeleton className="h-10 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {suggestionsError && (
            <div className="text-center py-8">
              <p className="text-destructive mb-4">{suggestionsError}</p>
              <Button onClick={() => loadSuggestions(true)} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          )}

          {showAiSuggestions && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {suggestions.slice(0, 3).map((suggestion, index) => (
                <Card
                  key={index}
                  className={`group overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg border ${
                    selectedSuggestion === index
                      ? "ring-2 ring-primary shadow-lg"
                      : "hover:shadow-md"
                  }`}
                  onClick={() => setSelectedSuggestion(index)}
                >
                  {/* Image Section */}
                  <div className="aspect-video relative overflow-hidden">
                    {suggestion.image ? (
                      <OptimizedImage
                        src={suggestion.image}
                        alt={suggestion.destination}
                        aspectRatio="video"
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                        attribution={suggestion.attribution}
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
                        <span className="text-primary text-lg font-medium">
                          {suggestion.destination}
                        </span>
                      </div>
                    )}
                    {/* Overlay gradient for better text readability */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                  </div>

                  <CardContent className="p-4">
                    {/* Destination and Tags */}
                    <div className="mb-3">
                      <h3 className="text-lg font-semibold text-foreground mb-2">
                        {suggestion.destination}
                      </h3>
                      <div className="flex flex-wrap gap-1.5 mb-3">
                        {suggestion.tags.slice(0, 3).map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                      {suggestion.description}
                    </p>

                    {/* Budget Section - Separate on Desktop */}
                    <div className="mb-4">
                      <div className="hidden md:block">
                        <div className="bg-muted/50 rounded-lg p-3 mb-4">
                          <span className="text-sm font-medium text-foreground">
                            Budget: {suggestion.budget}
                          </span>
                        </div>
                      </div>
                      <div className="md:hidden">
                        <span className="text-sm font-medium text-primary">
                          Budget: {suggestion.budget}
                        </span>
                      </div>
                    </div>

                    {/* Action Button */}
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleCreateTrip(index)
                      }}
                      className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-medium transition-all duration-300"
                      disabled={redirecting}
                    >
                      Create This Trip
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <Button
            variant="outline"
            size="lg"
            onClick={handleSkip}
            disabled={redirecting}
            className="border-muted-foreground/20 text-muted-foreground hover:text-foreground hover:bg-muted/50 transition-all duration-300"
          >
            <SkipForward className="h-5 w-5 mr-2" />I would like to explore the app on my own
          </Button>
        </div>
      </div>
    </div>
  )
}
