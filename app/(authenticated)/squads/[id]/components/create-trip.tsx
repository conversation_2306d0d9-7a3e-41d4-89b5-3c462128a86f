"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { usePara<PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import {
  useIsUserSubscribed,
  useCanCreateMoreTripsInSquad,
  useUserSubscriptionWithInit,
} from "@/lib/domains/user-subscription/user-subscription.hooks"
import { SubscriptionErrorType } from "@/lib/domains/user-subscription/user-subscription.types"
import { CalendarIcon, Loader2, ArrowLeft } from "lucide-react"
import { format, addDays } from "date-fns"
import { cn } from "@/lib/utils"
import { Calendar } from "@/components/ui/calendar"
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { getLocationImageByPlaceId } from "@/lib/google-places"
import { OptimizedImage } from "@/components/optimized-image"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { PageLoading } from "@/components/page-loading"

// Import domain-specific hooks and types
import { TripCreateData, TripFormData } from "@/lib/domains/trip/trip.types"
import { useCreateTrip } from "@/lib/domains/trip/trip.hooks"
import { useRealtimeSquad } from "@/lib/domains/squad/squad.realtime.hooks"

// Import the destination autocomplete component
import { DestinationAutocomplete } from "@/app/(authenticated)/trips/create/components/destination-autocomplete"

export default function CreateTrip() {
  const params = useParams()
  const squadId = params.id as string
  const { user, loading: authLoading } = useAuthStatus()
  const router = useRouter()
  const isSubscribed = useIsUserSubscribed()
  const canCreateMoreTripsInSquadFunc = useCanCreateMoreTripsInSquad()
  const { handleSubscriptionError } = useUserSubscriptionWithInit()

  // Use the squad hook to get squad data
  const { squad, loading: loadingSquad, error: squadError } = useRealtimeSquad(squadId)

  // Use the trip creation hook
  const { create, creating, error: createError } = useCreateTrip()

  // Form state
  const [formData, setFormData] = useState<TripFormData>({
    name: "",
    destination: "",
    placeId: undefined,
    startDate: null,
    endDate: null,
    budget: 1000,
    description: "",
  })

  // UI state
  const [canCreateTrip, setCanCreateTrip] = useState(true)
  const [checkingLimits, setCheckingLimits] = useState(false)
  const [locationImage, setLocationImage] = useState<string | null>(null)
  const [imageAttribution, setImageAttribution] = useState<{
    name: string
    photoReference?: string
    username?: string
    link?: string
  } | null>(null)
  const [loadingImage, setLoadingImage] = useState(false)
  const [lastFetchedLocation, setLastFetchedLocation] = useState<{
    placeId: string
    name: string
  } | null>(null)

  // Check if the user can create a trip in this squad
  useEffect(() => {
    if (authLoading || !user || !squadId || isSubscribed) return
    const checkTripLimit = async () => {
      setCheckingLimits(true)
      try {
        const canCreate = await canCreateMoreTripsInSquadFunc(user.uid, squadId)
        setCanCreateTrip(canCreate)
      } catch (error) {
        console.error("Error checking trip limits:", error)
        setCanCreateTrip(true) // Default to allowing creation
      } finally {
        setCheckingLimits(false)
      }
    }

    checkTripLimit()
  }, [isSubscribed, squadId, canCreateMoreTripsInSquadFunc, user?.uid, authLoading])

  // Fetch location image when place ID changes
  useEffect(() => {
    if (
      formData.placeId &&
      (!lastFetchedLocation || lastFetchedLocation.placeId !== formData.placeId)
    ) {
      const fetchLocationImage = async () => {
        setLoadingImage(true)
        try {
          const imageResult = await getLocationImageByPlaceId(
            formData.placeId as string,
            formData.destination
          )
          setLocationImage(imageResult.url)
          if (imageResult.attribution) {
            setImageAttribution(imageResult.attribution)
          }

          // Update last fetched location
          setLastFetchedLocation({
            placeId: formData.placeId as string,
            name: formData.destination,
          })
        } catch (error) {
          console.error("Error fetching location image:", error)
          setLocationImage(null)
          setImageAttribution(null)
        } finally {
          setLoadingImage(false)
        }
      }

      fetchLocationImage()
    }
  }, [formData.placeId, formData.destination, lastFetchedLocation])

  // Handle form field changes
  const handleChange = (field: keyof TripFormData, value: any, placeId?: string) => {
    setFormData((prev) => {
      const newData = { ...prev, [field]: value }

      // If this is a destination change with a place ID, update that too
      if (field === "destination" && placeId !== undefined) {
        newData.placeId = placeId
      }

      return newData
    })
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user || !squad) return

    // Validate required fields
    if (!formData.name || !formData.destination || !formData.startDate || !formData.endDate) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // Validate that startDate is in the future (not today or earlier)
    const today = new Date()
    today.setHours(0, 0, 0, 0) // Reset time to start of day for accurate comparison

    if (formData.startDate && formData.startDate <= today) {
      toast({
        title: "Invalid start date",
        description: "Start date must be in the future (not today or earlier).",
        variant: "destructive",
      })
      return
    }

    // Double-check subscription limits before creating
    if (!isSubscribed) {
      setCheckingLimits(true)
      const canCreate = await canCreateMoreTripsInSquadFunc(user.uid, squadId)
      setCheckingLimits(false)

      if (!canCreate) {
        // Use centralized error handling
        handleSubscriptionError(SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED)
        return
      }
    }

    try {
      // Prepare trip data
      const tripData: TripCreateData = {
        name: formData.name,
        destination: formData.destination,
        squadId: squadId,
        startDate: formData.startDate as any,
        endDate: formData.endDate as any,
        budget: formData.budget,
        description: formData.description,
        // Include place ID, locationThumbnail, and imageAttribution if available
        ...(formData.placeId ? { placeId: formData.placeId } : {}),
        ...(locationImage ? { locationThumbnail: locationImage } : {}),
        ...(imageAttribution ? { imageAttribution } : {}),
        status: "planning",
        attendees: [], // Will be populated from squad members after creation
        leaderId: user.uid,
        createdBy: user.uid,
      }

      // Create the trip using the hook
      const tripId = await create(tripData)

      if (!tripId) {
        throw new Error("Failed to create trip")
      }

      toast({
        title: "Trip created!",
        description: "Your trip has been created successfully.",
      })

      // Redirect to the trip page
      router.push(`/trips/${tripId}`)
    } catch (error) {
      console.error("Error creating trip:", error)
      toast({
        title: "Error",
        description: createError?.message || "Failed to create trip. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Show loading state
  if (loadingSquad) {
    return <PageLoading />
  }

  // Show error if squad not found
  if (squadError || !squad) {
    return (
      <div className="container py-10">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {squadError?.message || "Squad not found. Please try again."}
          </AlertDescription>
        </Alert>
        <div className="mt-4">
          <Link href="/squads">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Squads
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6">
      <div className="flex items-center mb-6">
        <Link href={`/squads/${squadId}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Squad
          </Button>
        </Link>
        <h1 className="text-2xl font-bold ml-4">Create a Trip for {squad.name}</h1>
      </div>

      {!canCreateTrip && !isSubscribed && (
        <Alert className="mb-6">
          <AlertTitle>Subscription Limit Reached</AlertTitle>
          <AlertDescription>
            You've reached the maximum number of trips allowed for this squad on the free plan.
            Upgrade to create more trips.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Trip Details</CardTitle>
          <CardDescription>Fill in the details for your new trip</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Trip Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleChange("name", e.target.value)}
                    placeholder="Summer Vacation 2023"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <DestinationAutocomplete
                    value={formData.destination}
                    onChange={(value, placeId) => handleChange("destination", value, placeId)}
                    placeholder="Miami, Florida"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !formData.startDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.startDate ? (
                            format(formData.startDate, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.startDate || undefined}
                          onSelect={(date) => {
                            handleChange("startDate", date)
                            // If end date is not set or is before start date, set it to start date + 3 days
                            if (!formData.endDate || (date && formData.endDate < date)) {
                              handleChange("endDate", date ? addDays(date, 3) : null)
                            }
                          }}
                          initialFocus
                          disabled={(date) => {
                            // Disable today and past dates
                            const today = new Date()
                            today.setHours(0, 0, 0, 0) // Reset time to start of day for accurate comparison
                            return date <= today
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="space-y-2">
                    <Label>End Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !formData.endDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.endDate ? (
                            format(formData.endDate, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.endDate || undefined}
                          onSelect={(date) => handleChange("endDate", date)}
                          disabled={(date) => {
                            // Get today's date with time set to start of day
                            const today = new Date()
                            today.setHours(0, 0, 0, 0)

                            // If startDate is selected, disable dates before startDate
                            // Otherwise, disable today and past dates
                            return (
                              (formData.startDate ? date < formData.startDate : date <= today) ||
                              date < new Date("1900-01-01")
                            )
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget">Budget (per person)</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
                    <Input
                      id="budget"
                      type="text"
                      pattern="[0-9]*"
                      inputMode="numeric"
                      value={formData.budget}
                      onChange={(e) => {
                        // Only allow whole numbers (no decimals)
                        const value = e.target.value && parseInt(e.target.value)
                        handleChange("budget", value || 0)
                      }}
                      className="pl-7"
                      placeholder="Enter budget amount"
                      maxLength={10}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {locationImage ? (
                  <div className="space-y-1">
                    <div className="rounded-lg overflow-hidden">
                      {loadingImage ? (
                        <div className="absolute inset-0 flex items-center justify-center bg-muted z-10">
                          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                        </div>
                      ) : null}
                      <OptimizedImage
                        src={locationImage}
                        alt={formData.destination}
                        aspectRatio="video"
                        className="rounded-lg"
                        onLoad={() => setLoadingImage(false)}
                        priority
                        quality={90}
                      />
                    </div>
                    {imageAttribution && (
                      <div className="text-xs text-muted-foreground text-right">
                        {imageAttribution.link ? (
                          <>
                            Photo by{" "}
                            <a
                              href={imageAttribution.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:underline"
                            >
                              {imageAttribution.name}
                            </a>{" "}
                            on{" "}
                            <a
                              href="https://unsplash.com"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:underline"
                            >
                              Unsplash
                            </a>
                          </>
                        ) : (
                          <>Photo of {imageAttribution.name} via Google Places</>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="rounded-lg bg-muted h-48 flex items-center justify-center">
                    <p className="text-muted-foreground">
                      Enter a destination to see a preview image
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleChange("description", e.target.value)}
                    placeholder="Enter trip description"
                    rows={5}
                  />
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Link href={`/squads/${squadId}`}>
              <Button variant="outline">Cancel</Button>
            </Link>
            <Button
              type="submit"
              disabled={creating || (squadId && !canCreateTrip) || checkingLimits}
            >
              {(creating || checkingLimits) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Trip
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
