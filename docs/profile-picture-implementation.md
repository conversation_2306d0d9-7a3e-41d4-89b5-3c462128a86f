# Profile Picture Implementation

This document describes the implementation of profile picture upload functionality across the Togeda.ai application.

## Overview

The profile picture functionality allows users to:
1. Upload a profile picture during signup (optional)
2. Update or remove their profile picture in settings
3. View profile pictures of other users in trip attendee lists

## Technical Implementation

### Server Actions

#### 1. Signup Profile Picture Upload
- **File**: `app/signup/actions/upload-profile-picture.ts`
- **Function**: `uploadProfilePictureAction(formData: FormData)`
- **Purpose**: Uploads profile picture to Vercel Blob storage during signup
- **Validation**: 1MB max size, image formats only (JPEG, PNG, WebP)

#### 2. User Creation with Profile
- **File**: `app/signup/actions/create-user-with-profile.ts`
- **Function**: `createUserWithProfileAction(userData, profilePictureFormData?)`
- **Purpose**: Creates user account with optional profile picture
- **Features**: Integrates Firebase Auth, Firestore, and Blob storage

#### 3. Profile Picture Management
- **File**: `app/(authenticated)/settings/actions/manage-profile-picture.ts`
- **Functions**: 
  - `uploadProfilePictureAction(userId, formData)`
  - `removeProfilePictureAction(userId, currentPhotoURL?)`
- **Purpose**: Update or remove profile pictures in settings
- **Features**: Automatic cleanup of old images

### UI Components

#### 1. Signup Form Enhancement
- **File**: `app/signup/components/signup-form.tsx`
- **Features**:
  - Optional profile picture upload in step 4
  - File validation (type and size)
  - Image preview with remove option
  - Integration with server action

#### 2. Settings Profile Management
- **File**: `app/(authenticated)/settings/components/profile-settings.tsx`
- **Features**:
  - Upload new profile picture
  - Remove existing profile picture
  - Loading states and error handling
  - Automatic page refresh after changes

#### 3. Dashboard Trip Display
- **File**: `app/(authenticated)/dashboard/components/UpcomingTripsTab.tsx`
- **Features**:
  - Display attendee profile pictures
  - Fallback to generated avatars using `avatar-utils.ts`
  - Stacked avatar layout with overflow indicator
  - Real-time updates

### Data Flow

#### 1. Signup Flow
```
User selects image → Client validation → Upload to Blob → Create user with photoURL
```

#### 2. Settings Update Flow
```
User uploads image → Upload to Blob → Update Firestore → Delete old image → Refresh UI
```

#### 3. Display Flow
```
Load trip data → Fetch attendee details → Display avatars with fallbacks
```

### Real-time Data Hooks

#### New Hook: `useRealtimeTripsAttendeesDetails`
- **File**: `lib/domains/user-trip/user-trip.realtime.hooks.ts`
- **Purpose**: Fetch attendee details for multiple trips efficiently
- **Usage**: Dashboard to show profile pictures in trip cards

### File Storage

- **Storage**: Vercel Blob Storage
- **Path Structure**: 
  - Signup: `profile-pictures/{timestamp}.{ext}`
  - Settings: `profile-pictures/{userId}/{timestamp}.{ext}`
- **Access**: Public read access
- **Cleanup**: Automatic deletion of old images when updating

### Validation Rules

- **File Size**: Maximum 1MB
- **File Types**: JPEG, JPG, PNG, WebP only
- **Required**: Profile pictures are optional everywhere
- **Fallbacks**: Generated avatars using `avatar-utils.ts`

### Error Handling

- Client-side validation with toast notifications
- Server-side validation with detailed error messages
- Graceful fallbacks for missing or failed images
- Automatic cleanup on errors

### Security Considerations

- File type validation on both client and server
- File size limits enforced
- User authentication required for uploads
- Secure blob storage with public read access only

## Usage Examples

### Signup with Profile Picture
```typescript
// User selects file, validation happens automatically
// Server action handles upload and user creation
const result = await createUserWithProfileAction(userData, profilePictureFormData)
```

### Update Profile Picture in Settings
```typescript
// Upload new picture
const result = await uploadProfilePictureAction(userId, formData)

// Remove existing picture
const result = await removeProfilePictureAction(userId, currentPhotoURL)
```

### Display Profile Pictures
```typescript
// Get attendee details with profile pictures
const { tripsAttendeesDetails } = useRealtimeTripsAttendeesDetails(tripIds)

// Display with fallback
<AvatarImage src={getBestAvatar(user.photoURL, user.displayName, 24)} />
```

## Testing

The implementation has been tested for:
- ✅ Compilation without errors
- ✅ Development server startup
- ✅ UI component rendering
- ✅ Type safety across all components

## Future Enhancements

1. Image compression before upload
2. Multiple image sizes/thumbnails
3. Drag and drop upload interface
4. Bulk avatar loading optimization
5. Image cropping functionality
